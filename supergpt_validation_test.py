#!/usr/bin/env python3
"""
SUPER GPT INTEGRATION VALIDATION TEST
Comprehensive validation of all Super GPT AI functions and capabilities
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import system components
from bybit_bot.core.logger import TradingBotLogger

class SuperGPTValidationTest:
    """Comprehensive validation of Super GPT integration and AI functions"""
    
    def __init__(self):
        self.logger = TradingBotLogger("SuperGPTValidation")
        self.validation_results = {
            "supergpt_integration": {"status": "NOT_TESTED", "components": []},
            "natural_language_processing": {"status": "NOT_TESTED", "components": []},
            "advanced_reasoning_engine": {"status": "NOT_TESTED", "components": []},
            "predictive_modeling": {"status": "NOT_TESTED", "components": []},
            "market_psychology_analysis": {"status": "NOT_TESTED", "components": []},
            "sentiment_processing": {"status": "NOT_TESTED", "components": []},
            "memory_management": {"status": "NOT_TESTED", "components": []},
            "meta_cognition_engine": {"status": "NOT_TESTED", "components": []},
            "openrouter_client": {"status": "NOT_TESTED", "components": []},
            "self_correcting_systems": {"status": "NOT_TESTED", "components": []},
            "overall_ai_score": 0,
            "critical_issues": [],
            "recommendations": []
        }
        
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run complete Super GPT validation"""
        self.logger.info("Starting comprehensive Super GPT integration validation...")
        
        try:
            # Validation 1: SuperGPT Integration Core
            await self._validate_supergpt_integration()
            
            # Validation 2: Natural Language Processing
            await self._validate_natural_language_processing()
            
            # Validation 3: Advanced Reasoning Engine
            await self._validate_advanced_reasoning_engine()
            
            # Validation 4: Predictive Modeling
            await self._validate_predictive_modeling()
            
            # Validation 5: Market Psychology Analysis
            await self._validate_market_psychology_analysis()
            
            # Validation 6: Sentiment Processing
            await self._validate_sentiment_processing()
            
            # Validation 7: Memory Management
            await self._validate_memory_management()
            
            # Validation 8: Meta-Cognition Engine
            await self._validate_meta_cognition_engine()
            
            # Validation 9: OpenRouter Client
            await self._validate_openrouter_client()
            
            # Validation 10: Self-Correcting Systems
            await self._validate_self_correcting_systems()
            
            # Calculate overall AI score
            self._calculate_ai_score()
            
            # Generate recommendations
            self._generate_recommendations()
            
        except Exception as e:
            self.logger.error(f"Validation failed: {e}")
            self.validation_results["error"] = str(e)
        
        return self.validation_results
    
    async def _validate_supergpt_integration(self):
        """Validate SuperGPT integration core"""
        try:
            self.logger.info("Validating SuperGPT integration core...")
            
            components = []
            
            # Test SuperGPT integration import
            try:
                from bybit_bot.ai.supergpt_integration import SuperGPTIntegration
                components.append({"name": "SuperGPTIntegration", "status": "ACTIVE"})
                
                # Test basic initialization
                supergpt = SuperGPTIntegration()
                components.append({"name": "SuperGPT_Initialization", "status": "ACTIVE"})
                
            except Exception as e:
                components.append({"name": "SuperGPTIntegration", "status": "FAILED", "error": str(e)})
            
            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)
            
            self.validation_results["supergpt_integration"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"SuperGPT integration validation: {active_count}/{total_count} components active")
            
        except Exception as e:
            self.logger.error(f"SuperGPT integration validation failed: {e}")
            self.validation_results["supergpt_integration"]["status"] = "FAILED"
    
    async def _validate_natural_language_processing(self):
        """Validate natural language processing capabilities"""
        try:
            self.logger.info("Validating natural language processing...")
            
            components = []
            
            # Test OpenRouter client for NLP
            try:
                from bybit_bot.ai.openrouter_client import OpenRouterClient
                components.append({"name": "OpenRouterClient", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "OpenRouterClient", "status": "FAILED", "error": str(e)})
            
            # Test sentiment analysis components
            try:
                from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
                components.append({"name": "NewsSentimentCrawler", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "NewsSentimentCrawler", "status": "FAILED", "error": str(e)})
            
            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)
            
            self.validation_results["natural_language_processing"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"NLP validation: {active_count}/{total_count} components active")
            
        except Exception as e:
            self.logger.error(f"NLP validation failed: {e}")
            self.validation_results["natural_language_processing"]["status"] = "FAILED"
    
    async def _validate_advanced_reasoning_engine(self):
        """Validate advanced reasoning engine"""
        try:
            self.logger.info("Validating advanced reasoning engine...")
            
            components = []
            
            # Test meta-cognition engine
            try:
                from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
                components.append({"name": "MetaCognitionEngine", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "MetaCognitionEngine", "status": "FAILED", "error": str(e)})
            
            # Test recursive improvement system
            try:
                from bybit_bot.ai.recursive_improvement_system import RecursiveImprovementSystem
                components.append({"name": "RecursiveImprovementSystem", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "RecursiveImprovementSystem", "status": "FAILED", "error": str(e)})
            
            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)
            
            self.validation_results["advanced_reasoning_engine"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"Advanced reasoning validation: {active_count}/{total_count} components active")
            
        except Exception as e:
            self.logger.error(f"Advanced reasoning validation failed: {e}")
            self.validation_results["advanced_reasoning_engine"]["status"] = "FAILED"
    
    async def _validate_predictive_modeling(self):
        """Validate predictive modeling capabilities"""
        try:
            self.logger.info("Validating predictive modeling...")
            
            components = []
            
            # Test market predictor
            try:
                from bybit_bot.ml.market_predictor import MarketPredictor
                components.append({"name": "MarketPredictor", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "MarketPredictor", "status": "FAILED", "error": str(e)})
            
            # Test performance analyzer
            try:
                from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer
                components.append({"name": "PerformanceAnalyzer", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "PerformanceAnalyzer", "status": "FAILED", "error": str(e)})
            
            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)
            
            self.validation_results["predictive_modeling"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"Predictive modeling validation: {active_count}/{total_count} components active")
            
        except Exception as e:
            self.logger.error(f"Predictive modeling validation failed: {e}")
            self.validation_results["predictive_modeling"]["status"] = "FAILED"
    
    async def _validate_market_psychology_analysis(self):
        """Validate market psychology analysis"""
        try:
            self.logger.info("Validating market psychology analysis...")
            
            components = []
            
            # Test social sentiment crawler
            try:
                from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
                components.append({"name": "SocialSentimentCrawler", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "SocialSentimentCrawler", "status": "FAILED", "error": str(e)})
            
            # Test economic data crawler
            try:
                from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
                components.append({"name": "EconomicDataCrawler", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "EconomicDataCrawler", "status": "FAILED", "error": str(e)})
            
            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)
            
            self.validation_results["market_psychology_analysis"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"Market psychology validation: {active_count}/{total_count} components active")
            
        except Exception as e:
            self.logger.error(f"Market psychology validation failed: {e}")
            self.validation_results["market_psychology_analysis"]["status"] = "FAILED"

    async def _validate_sentiment_processing(self):
        """Validate sentiment processing capabilities"""
        try:
            self.logger.info("Validating sentiment processing...")

            components = []

            # Test news sentiment crawler
            try:
                from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
                components.append({"name": "NewsSentimentCrawler", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "NewsSentimentCrawler", "status": "FAILED", "error": str(e)})

            # Test social sentiment crawler
            try:
                from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
                components.append({"name": "SocialSentimentCrawler", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "SocialSentimentCrawler", "status": "FAILED", "error": str(e)})

            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)

            self.validation_results["sentiment_processing"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Sentiment processing validation: {active_count}/{total_count} components active")

        except Exception as e:
            self.logger.error(f"Sentiment processing validation failed: {e}")
            self.validation_results["sentiment_processing"]["status"] = "FAILED"

    async def _validate_memory_management(self):
        """Validate memory management systems"""
        try:
            self.logger.info("Validating memory management...")

            components = []

            # Test persistent memory manager
            try:
                from bybit_bot.ai.memory_manager import PersistentMemoryManager
                components.append({"name": "PersistentMemoryManager", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "PersistentMemoryManager", "status": "FAILED", "error": str(e)})

            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)

            self.validation_results["memory_management"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Memory management validation: {active_count}/{total_count} components active")

        except Exception as e:
            self.logger.error(f"Memory management validation failed: {e}")
            self.validation_results["memory_management"]["status"] = "FAILED"

    async def _validate_meta_cognition_engine(self):
        """Validate meta-cognition engine"""
        try:
            self.logger.info("Validating meta-cognition engine...")

            components = []

            # Test meta-cognition engine
            try:
                from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
                components.append({"name": "MetaCognitionEngine", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "MetaCognitionEngine", "status": "FAILED", "error": str(e)})

            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)

            self.validation_results["meta_cognition_engine"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Meta-cognition validation: {active_count}/{total_count} components active")

        except Exception as e:
            self.logger.error(f"Meta-cognition validation failed: {e}")
            self.validation_results["meta_cognition_engine"]["status"] = "FAILED"

    async def _validate_openrouter_client(self):
        """Validate OpenRouter client"""
        try:
            self.logger.info("Validating OpenRouter client...")

            components = []

            # Test OpenRouter client
            try:
                from bybit_bot.ai.openrouter_client import OpenRouterClient
                components.append({"name": "OpenRouterClient", "status": "ACTIVE"})

                # Test basic client functionality
                client = OpenRouterClient()
                components.append({"name": "OpenRouter_Initialization", "status": "ACTIVE"})

            except Exception as e:
                components.append({"name": "OpenRouterClient", "status": "FAILED", "error": str(e)})

            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)

            self.validation_results["openrouter_client"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"OpenRouter validation: {active_count}/{total_count} components active")

        except Exception as e:
            self.logger.error(f"OpenRouter validation failed: {e}")
            self.validation_results["openrouter_client"]["status"] = "FAILED"

    async def _validate_self_correcting_systems(self):
        """Validate self-correcting systems"""
        try:
            self.logger.info("Validating self-correcting systems...")

            components = []

            # Test self-correcting code evolution
            try:
                from bybit_bot.ai.self_correcting_code_evolution import SelfCorrectingCodeEvolution
                components.append({"name": "SelfCorrectingCodeEvolution", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "SelfCorrectingCodeEvolution", "status": "FAILED", "error": str(e)})

            # Test self-healing system
            try:
                from bybit_bot.core.self_healing import SelfHealingSystem
                components.append({"name": "SelfHealingSystem", "status": "ACTIVE"})
            except Exception as e:
                components.append({"name": "SelfHealingSystem", "status": "FAILED", "error": str(e)})

            active_count = len([c for c in components if c["status"] == "ACTIVE"])
            total_count = len(components)

            self.validation_results["self_correcting_systems"] = {
                "status": "ACTIVE" if active_count == total_count else "PARTIAL" if active_count > 0 else "FAILED",
                "components": components,
                "score": active_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Self-correcting systems validation: {active_count}/{total_count} components active")

        except Exception as e:
            self.logger.error(f"Self-correcting systems validation failed: {e}")
            self.validation_results["self_correcting_systems"]["status"] = "FAILED"

    def _calculate_ai_score(self):
        """Calculate overall AI score"""
        try:
            categories = [
                "supergpt_integration", "natural_language_processing", "advanced_reasoning_engine",
                "predictive_modeling", "market_psychology_analysis", "sentiment_processing",
                "memory_management", "meta_cognition_engine", "openrouter_client", "self_correcting_systems"
            ]

            total_score = 0
            valid_categories = 0

            for category in categories:
                if category in self.validation_results and "score" in self.validation_results[category]:
                    total_score += self.validation_results[category]["score"]
                    valid_categories += 1

            self.validation_results["overall_ai_score"] = total_score / valid_categories if valid_categories > 0 else 0

            self.logger.info(f"Overall AI score: {self.validation_results['overall_ai_score']:.2%}")

        except Exception as e:
            self.logger.error(f"Error calculating AI score: {e}")
            self.validation_results["overall_ai_score"] = 0

    def _generate_recommendations(self):
        """Generate recommendations based on validation results"""
        try:
            recommendations = []

            # Check each category for issues
            for category, results in self.validation_results.items():
                if isinstance(results, dict) and "status" in results:
                    if results["status"] == "FAILED":
                        recommendations.append(f"CRITICAL: {category.replace('_', ' ').title()} system is not functional")
                    elif results["status"] == "PARTIAL":
                        recommendations.append(f"WARNING: {category.replace('_', ' ').title()} system has partial functionality")

            # Overall score recommendations
            score = self.validation_results["overall_ai_score"]
            if score < 0.5:
                recommendations.append("CRITICAL: Overall AI score is below 50% - major AI system issues detected")
            elif score < 0.8:
                recommendations.append("WARNING: Overall AI score is below 80% - AI optimization needed")
            else:
                recommendations.append("SUCCESS: AI systems are operating at high efficiency")

            self.validation_results["recommendations"] = recommendations

        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            self.validation_results["recommendations"] = ["Error generating recommendations"]

async def main():
    """Main validation function"""
    validation = SuperGPTValidationTest()
    results = await validation.run_comprehensive_validation()

    print("\n" + "="*80)
    print("SUPER GPT INTEGRATION VALIDATION RESULTS")
    print("="*80)

    # Display results by category
    categories = [
        "supergpt_integration", "natural_language_processing", "advanced_reasoning_engine",
        "predictive_modeling", "market_psychology_analysis", "sentiment_processing",
        "memory_management", "meta_cognition_engine", "openrouter_client", "self_correcting_systems"
    ]

    for category in categories:
        if category in results:
            result = results[category]
            status = result.get("status", "UNKNOWN")
            score = result.get("score", 0)
            print(f"{category.replace('_', ' ').title():<30}: {status:<8} ({score:.1%})")

    print("-"*80)
    print(f"Overall AI Score: {results['overall_ai_score']:.1%}")
    print("-"*80)

    # Display recommendations
    if results.get("recommendations"):
        print("\nRECOMMENDATIONS:")
        for i, rec in enumerate(results["recommendations"], 1):
            print(f"{i}. {rec}")

    print("="*80)

    # Save results to file
    with open("supergpt_validation_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    return results

if __name__ == "__main__":
    asyncio.run(main())
