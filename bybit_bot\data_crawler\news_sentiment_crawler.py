# Direct import - safe_correlation module is available

"""
News Sentiment Crawler
Collects and analyzes news sentiment from multiple sources
"""

import asyncio
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import aiohttp
# Make imports optional with fallbacks
try:
    import feedparser
    FEEDPARSER_AVAILABLE = True
except ImportError:
    FEEDPARSER_AVAILABLE = False
    class feedparser:
        @staticmethod
        def parse(url): return {'entries': []}

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False
    class TextBlob:
        def __init__(self, text): self.text = text
        @property
        def sentiment(self): return type('obj', (object,), {'polarity': 0.0, 'subjectivity': 0.0})()

try:
    from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
    VADER_AVAILABLE = True
except ImportError:
    VADER_AVAILABLE = False
    class SentimentIntensityAnalyzer:
        def polarity_scores(self, text): return {'compound': 0.0, 'pos': 0.0, 'neu': 1.0, 'neg': 0.0}

try:
    import newspaper
    NEWSPAPER_AVAILABLE = True
except ImportError:
    NEWSPAPER_AVAILABLE = False
    class newspaper:
        @staticmethod
        def Article(url): return type('obj', (object,), {'download': lambda: None, 'parse': lambda: None, 'text': ''})()

if not all([FEEDPARSER_AVAILABLE, TEXTBLOB_AVAILABLE, VADER_AVAILABLE, NEWSPAPER_AVAILABLE]):
    print("[WARNING] Some news sentiment analysis dependencies not available - using fallbacks")

from ..core.config import BotConfig
from ..database.connection import DatabaseManager


class NewsSentimentCrawler:
    """
    Advanced news sentiment crawler that:
    - Collects news from multiple sources
    - Performs sentiment analysis
    - Identifies market-moving events
    - Tracks news impact on prices
    """
    
    def __init__(self, config: BotConfig, db_manager: DatabaseManager):
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        self.running = False
        
        # Initialize sentiment analyzers
        self.vader_analyzer = SentimentIntensityAnalyzer()
        
        # News sources configuration
        self.news_sources = {
            'coindesk': {
                'rss': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
                'base_url': 'https://www.coindesk.com',
                'keywords': ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi']
            },
            'cointelegraph': {
                'rss': 'https://cointelegraph.com/rss',
                'base_url': 'https://cointelegraph.com',
                'keywords': ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'altcoin']
            },
            'decrypt': {
                'rss': 'https://decrypt.co/feed',
                'base_url': 'https://decrypt.co',
                'keywords': ['bitcoin', 'ethereum', 'crypto', 'nft', 'web3']
            },
            'cryptonews': {
                'rss': 'https://cryptonews.com/news/feed',
                'base_url': 'https://cryptonews.com',
                'keywords': ['bitcoin', 'ethereum', 'crypto', 'trading']
            }
        }
        
        # NewsAPI configuration
        self.newsapi_key = self.config.get_api_key("newsapi").get("api_key")
        
        self.crawl_tasks = []
        
    async def initialize(self):
        """Initialize the news sentiment crawler"""
        try:
            self.logger.info("Initializing News Sentiment Crawler...")
            
            # Test database connection
            if self.db_manager:
                await self.db_manager.test_connection()
            
            # Validate API keys
            if not self.newsapi_key:
                self.logger.warning("NewsAPI key not found - some features may be limited")
            
            # Initialize sentiment analyzers
            self.vader_analyzer = SentimentIntensityAnalyzer()
            
            self.logger.info("News Sentiment Crawler initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize News Sentiment Crawler: {e}")
            return False
        
    async def start(self):
        """Start the news sentiment crawler"""
        if self.running:
            return
            
        self.running = True
        self.logger.info("📰 Starting news sentiment crawler...")
        
        # Start crawling tasks
        self.crawl_tasks = [
            asyncio.create_task(self._crawl_rss_feeds()),
            asyncio.create_task(self._crawl_newsapi()),
            asyncio.create_task(self._analyze_news_impact()),
            asyncio.create_task(self._track_trending_topics()),
        ]
        
        await asyncio.gather(*self.crawl_tasks, return_exceptions=True)
    
    async def stop(self):
        """Stop the news sentiment crawler"""
        self.running = False
        
        # Cancel all tasks
        for task in self.crawl_tasks:
            task.cancel()
            
        self.logger.info("🛑 News sentiment crawler stopped")
    
    async def _crawl_rss_feeds(self):
        """Crawl RSS feeds from crypto news sources"""
        while self.running:
            try:
                async with aiohttp.ClientSession() as session:
                    for source_name, source_config in self.news_sources.items():
                        try:
                            # Fetch RSS feed
                            async with session.get(source_config['rss']) as response:
                                if response.status == 200:
                                    rss_content = await response.text()
                                    
                                    # Parse RSS feed
                                    feed = feedparser.parse(rss_content)
                                    
                                    # Process each article
                                    for entry in feed.entries[:20]:  # Limit to 20 recent articles
                                        await self._process_news_article(source_name, entry, session)
                        
                        except Exception as e:
                            self.logger.error(f"Error crawling {source_name}: {e}")
                        
                        # Avoid overwhelming servers
                        await asyncio.sleep(2)
                
                await asyncio.sleep(self.config.data_crawler.news_interval)
                
            except Exception as e:
                self.logger.error(f"Error in RSS crawler: {e}")
                await asyncio.sleep(60)
    
    async def _crawl_newsapi(self):
        """Crawl news from NewsAPI"""
        if not self.newsapi_key:
            self.logger.warning("NewsAPI key not configured, skipping NewsAPI crawling")
            return
        
        while self.running:
            try:
                keywords = ['bitcoin', 'ethereum', 'cryptocurrency', 'blockchain', 'defi', 'nft']
                
                async with aiohttp.ClientSession() as session:
                    for keyword in keywords:
                        url = "https://newsapi.org/v2/everything"
                        params = {
                            'q': keyword,
                            'language': 'en',
                            'sortBy': 'publishedAt',
                            'pageSize': 20,
                            'apiKey': self.newsapi_key
                        }
                        
                        try:
                            async with session.get(url, params=params) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    
                                    for article in data.get('articles', []):
                                        await self._process_newsapi_article(keyword, article)
                        
                        except Exception as e:
                            self.logger.error(f"Error fetching NewsAPI for {keyword}: {e}")
                        
                        await asyncio.sleep(1)  # Rate limiting
                
                await asyncio.sleep(self.config.data_crawler.news_interval * 2)  # Less frequent for NewsAPI
                
            except Exception as e:
                self.logger.error(f"Error in NewsAPI crawler: {e}")
                await asyncio.sleep(120)
    
    async def _analyze_news_impact(self):
        """Analyze news impact on market movements"""
        while self.running:
            try:
                # Get recent news and price movements
                cutoff_time = datetime.utcnow() - timedelta(hours=24)
                
                # Get news from last 24 hours
                news_query = """
                SELECT * FROM news_sentiment 
                WHERE published_at > $1 
                ORDER BY published_at DESC
                """
                
                recent_news = await self.db_manager.fetch_all(news_query, cutoff_time)
                
                # Get price movements for analysis
                price_query = """
                SELECT symbol, timestamp, price, change_percent_24h 
                FROM market_data 
                WHERE timestamp > $1 
                ORDER BY timestamp DESC
                """
                
                price_data = await self.db_manager.fetch_all(price_query, cutoff_time)
                
                # Analyze correlations
                impact_analysis = await self._correlate_news_price_movements(recent_news, price_data)
                
                # Store impact analysis
                for analysis in impact_analysis:
                    await self._store_news_impact(analysis)
                
                await asyncio.sleep(3600)  # Analyze every hour
                
            except Exception as e:
                self.logger.error(f"Error in news impact analysis: {e}")
                await asyncio.sleep(1800)
    
    async def _track_trending_topics(self):
        """Track trending topics and keywords"""
        while self.running:
            try:
                # Get recent news
                cutoff_time = datetime.utcnow() - timedelta(hours=6)
                
                query = """
                SELECT title, content, extracted_keywords 
                FROM news_sentiment 
                WHERE published_at > $1
                """
                
                recent_news = await self.db_manager.fetch_all(query, cutoff_time)
                
                # Extract and count keywords
                keyword_counts = {}
                for news in recent_news:
                    keywords = news.get('extracted_keywords', '').split(',')
                    for keyword in keywords:
                        keyword = keyword.strip().lower()
                        if len(keyword) > 2:
                            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
                
                # Identify trending topics
                trending_topics = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:20]
                
                # Store trending topics
                await self._store_trending_topics(trending_topics)
                
                await asyncio.sleep(1800)  # Update every 30 minutes
                
            except Exception as e:
                self.logger.error(f"Error tracking trending topics: {e}")
                await asyncio.sleep(900)
    
    async def _process_news_article(self, source: str, entry: Dict, session: aiohttp.ClientSession):
        """Process a single news article from RSS feed"""
        try:
            # Check if article already exists
            existing = await self._check_article_exists(entry.link)
            if existing:
                return
            
            # Extract article content
            article_data = await self._extract_article_content(entry.link, session)
            if not article_data:
                return
            
            # Perform sentiment analysis
            sentiment_scores = self._analyze_sentiment(article_data['title'], article_data['content'])
            
            # Extract relevant keywords
            keywords = self._extract_keywords(article_data['title'], article_data['content'])
            
            # Determine relevance to crypto
            relevance_score = self._calculate_crypto_relevance(article_data['title'], article_data['content'])
            
            # Prepare news data
            news_data = {
                'source': source,
                'title': article_data['title'],
                'content': article_data['content'][:5000],  # Limit content length
                'url': entry.link,
                'published_at': self._parse_publish_date(entry),
                'sentiment_compound': sentiment_scores['compound'],
                'sentiment_positive': sentiment_scores['pos'],
                'sentiment_negative': sentiment_scores['neg'],
                'sentiment_neutral': sentiment_scores['neu'],
                'textblob_polarity': sentiment_scores['textblob_polarity'],
                'textblob_subjectivity': sentiment_scores['textblob_subjectivity'],
                'relevance_score': relevance_score,
                'extracted_keywords': ','.join(keywords),
                'timestamp': datetime.utcnow()
            }
            
            # Store news data
            await self._store_news_data(news_data)
            
        except Exception as e:
            self.logger.error(f"Error processing article {entry.link}: {e}")
    
    async def _process_newsapi_article(self, keyword: str, article: Dict):
        """Process a single article from NewsAPI"""
        try:
            # Check if article already exists
            existing = await self._check_article_exists(article['url'])
            if existing:
                return
            
            # Perform sentiment analysis
            title = article.get('title', '')
            content = article.get('description', '') + ' ' + article.get('content', '')
            
            sentiment_scores = self._analyze_sentiment(title, content)
            
            # Extract keywords
            keywords = self._extract_keywords(title, content)
            keywords.append(keyword)  # Add search keyword
            
            # Calculate relevance
            relevance_score = self._calculate_crypto_relevance(title, content)
            
            # Prepare news data
            news_data = {
                'source': 'newsapi',
                'title': title,
                'content': content[:5000],
                'url': article['url'],
                'published_at': datetime.fromisoformat(article['publishedAt'].replace('Z', '+00:00')),
                'sentiment_compound': sentiment_scores['compound'],
                'sentiment_positive': sentiment_scores['pos'],
                'sentiment_negative': sentiment_scores['neg'],
                'sentiment_neutral': sentiment_scores['neu'],
                'textblob_polarity': sentiment_scores['textblob_polarity'],
                'textblob_subjectivity': sentiment_scores['textblob_subjectivity'],
                'relevance_score': relevance_score,
                'extracted_keywords': ','.join(list(set(keywords))),
                'timestamp': datetime.utcnow()
            }
            
            # Store news data
            await self._store_news_data(news_data)
            
        except Exception as e:
            self.logger.error(f"Error processing NewsAPI article: {e}")
    
    async def _extract_article_content(self, url: str, session: aiohttp.ClientSession) -> Optional[Dict]:
        """Extract article content from URL"""
        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    html = await response.text()
                    
                    # Use newspaper3k for content extraction
                    article = newspaper.Article(url)
                    article.set_html(html)
                    article.parse()
                    
                    return {
                        'title': article.title,
                        'content': article.text,
                        'authors': article.authors,
                        'publish_date': article.publish_date
                    }
        except Exception as e:
            self.logger.error(f"Error extracting content from {url}: {e}")
            return None
    
    def _analyze_sentiment(self, title: str, content: str) -> Dict:
        """Analyze sentiment using multiple methods"""
        text = title + ' ' + content
        
        # VADER sentiment analysis
        vader_scores = self.vader_analyzer.polarity_scores(text)
        
        # TextBlob sentiment analysis
        blob = TextBlob(text)
        
        return {
            'compound': vader_scores['compound'],
            'pos': vader_scores['pos'],
            'neg': vader_scores['neg'],
            'neu': vader_scores['neu'],
            'textblob_polarity': blob.sentiment.polarity,
            'textblob_subjectivity': blob.sentiment.subjectivity
        }
    
    def _extract_keywords(self, title: str, content: str) -> List[str]:
        """Extract relevant keywords from text"""
        text = (title + ' ' + content).lower()
        
        # Crypto-related keywords to look for
        crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',
            'blockchain', 'defi', 'nft', 'altcoin', 'trading', 'exchange',
            'bull', 'bear', 'pump', 'dump', 'rally', 'crash', 'surge',
            'adoption', 'regulation', 'institutional', 'whale', 'volume',
            'breakout', 'resistance', 'support', 'technical analysis',
            'fundamental analysis', 'market cap', 'price prediction',
            'binance', 'coinbase', 'bybit', 'ftx', 'kraken'
        ]
        
        found_keywords = []
        for keyword in crypto_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        # Extract additional keywords using simple NLP
        words = re.findall(r'\b[a-z]{3,}\b', text)
        word_freq = {}
        for word in words:
            if word not in ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Add top frequent words
        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        found_keywords.extend([word for word, freq in top_words if freq > 2])
        
        return list(set(found_keywords))
    
    def _calculate_crypto_relevance(self, title: str, content: str) -> float:
        """Calculate relevance score to cryptocurrency"""
        text = (title + ' ' + content).lower()
        
        # High relevance keywords
        high_relevance = ['bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'trading']
        medium_relevance = ['digital', 'technology', 'financial', 'market', 'investment']
        low_relevance = ['news', 'report', 'analysis', 'update']
        
        score = 0
        for keyword in high_relevance:
            score += text.count(keyword) * 3
        
        for keyword in medium_relevance:
            score += text.count(keyword) * 1
        
        for keyword in low_relevance:
            score += text.count(keyword) * 0.5
        
        # Normalize score
        max_score = len(text.split()) * 0.1
        return min(score / max_score, 1.0) if max_score > 0 else 0
    
    def _parse_publish_date(self, entry: Dict) -> datetime:
        """Parse publish date from RSS entry"""
        try:
            if hasattr(entry, 'published_parsed') and entry.published_parsed:
                import time
                return datetime.fromtimestamp(time.mktime(entry.published_parsed))
            elif hasattr(entry, 'published'):
                from dateutil import parser
                return parser.parse(entry.published)
        except Exception:
            pass
        
        return datetime.utcnow()
    
    async def _check_article_exists(self, url: str) -> bool:
        """Check if article already exists in database"""
        query = "SELECT id FROM news_sentiment WHERE url = $1"
        result = await self.db_manager.fetch_one(query, url)
        return result is not None
    
    async def _correlate_news_price_movements(self, news_data: List, price_data: List) -> List[Dict]:
        """Correlate news sentiment with price movements"""
        correlations = []
        
        try:
            # Group news by time windows
            time_windows = {}
            for news in news_data:
                time_key = news['published_at'].replace(minute=0, second=0, microsecond=0)
                if time_key not in time_windows:
                    time_windows[time_key] = []
                time_windows[time_key].append(news)
            
            # Analyze each time window
            for time_key, window_news in time_windows.items():
                # Calculate average sentiment for the window
                avg_sentiment = sum(n['sentiment_compound'] for n in window_news) / len(window_news)
                
                # Find corresponding price movements
                window_start = time_key
                window_end = time_key + timedelta(hours=1)
                
                relevant_prices = [
                    p for p in price_data 
                    if window_start <= p['timestamp'] <= window_end
                ]
                
                if relevant_prices:
                    avg_price_change = sum(p['change_percent_24h'] for p in relevant_prices) / len(relevant_prices)
                    
                    correlation = {
                        'timestamp': time_key,
                        'news_count': len(window_news),
                        'avg_sentiment': avg_sentiment,
                        'avg_price_change': avg_price_change,
                        'correlation_strength': abs(avg_sentiment * avg_price_change),
                        'news_titles': [n['title'][:100] for n in window_news[:3]]
                    }
                    
                    correlations.append(correlation)
        
        except Exception as e:
            self.logger.error(f"Error correlating news and price movements: {e}")
        
        return correlations
    
    async def _store_news_data(self, data: Dict):
        """Store news data in database"""
        query = """
        INSERT INTO news_sentiment (source, title, content, url, published_at, sentiment_compound,
                                  sentiment_positive, sentiment_negative, sentiment_neutral,
                                  textblob_polarity, textblob_subjectivity, relevance_score,
                                  extracted_keywords, timestamp)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ON CONFLICT (url) DO NOTHING
        """
        
        await self.db_manager.execute(
            query,
            data['source'], data['title'], data['content'], data['url'], data['published_at'],
            data['sentiment_compound'], data['sentiment_positive'], data['sentiment_negative'],
            data['sentiment_neutral'], data['textblob_polarity'], data['textblob_subjectivity'],
            data['relevance_score'], data['extracted_keywords'], data['timestamp']
        )
    
    async def _store_news_impact(self, data: Dict):
        """Store news impact analysis in database"""
        query = """
        INSERT INTO news_impact_analysis (timestamp, news_count, avg_sentiment, avg_price_change,
                                        correlation_strength, news_titles)
        VALUES ($1, $2, $3, $4, $5, $6)
        """
        
        await self.db_manager.execute(
            query,
            data['timestamp'], data['news_count'], data['avg_sentiment'],
            data['avg_price_change'], data['correlation_strength'], 
            ','.join(data['news_titles'])
        )
    
    async def _store_trending_topics(self, topics: List[tuple]):
        """Store trending topics in database"""
        query = """
        INSERT INTO trending_topics (timestamp, keyword, count, rank)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (timestamp, keyword) DO UPDATE SET count = $3, rank = $4
        """
        
        timestamp = datetime.utcnow().replace(minute=0, second=0, microsecond=0)
        
        for rank, (keyword, count) in enumerate(topics, 1):
            await self.db_manager.execute(query, timestamp, keyword, count, rank)
    
    async def get_sentiment_summary(self, hours: int = 24) -> Dict:
        """Get sentiment summary for the last N hours"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        query = """
        SELECT 
            AVG(sentiment_compound) as avg_sentiment,
            AVG(relevance_score) as avg_relevance,
            COUNT(*) as total_news,
            COUNT(CASE WHEN sentiment_compound > 0.1 THEN 1 END) as positive_news,
            COUNT(CASE WHEN sentiment_compound < -0.1 THEN 1 END) as negative_news
        FROM news_sentiment 
        WHERE published_at > $1 AND relevance_score > 0.3
        """
        
        result = await self.db_manager.fetch_one(query, cutoff_time)
        return dict(result) if result else {}
    
    async def get_trending_topics(self, limit: int = 10) -> List[Dict]:
        """Get current trending topics"""
        cutoff_time = datetime.utcnow() - timedelta(hours=6)
        
        query = """
        SELECT keyword, SUM(count) as total_count
        FROM trending_topics 
        WHERE timestamp > $1
        GROUP BY keyword
        ORDER BY total_count DESC
        LIMIT $2
        """
        
        results = await self.db_manager.fetch_all(query, cutoff_time, limit)
        return [dict(row) for row in results]
    
    async def get_news_by_sentiment(self, sentiment_threshold: float = 0.5, limit: int = 20) -> List[Dict]:
        """Get news articles by sentiment threshold"""
        query = """
        SELECT title, url, sentiment_compound, published_at, source
        FROM news_sentiment 
        WHERE ABS(sentiment_compound) > $1 AND relevance_score > 0.5
        ORDER BY published_at DESC
        LIMIT $2
        """
        
        results = await self.db_manager.fetch_all(query, sentiment_threshold, limit)
        return [dict(row) for row in results]
