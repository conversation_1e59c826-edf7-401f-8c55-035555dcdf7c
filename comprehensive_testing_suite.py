#!/usr/bin/env python3
"""
COMPREHENSIVE TESTING SUITE
Complete system test that mirrors main.py execution to verify entire unified system
"""

import sys
import os
import time
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import system components
from bybit_bot.core.logger import TradingBotLogger

class ComprehensiveTestingSuite:
    """Comprehensive testing suite that mirrors complete main.py execution"""
    
    def __init__(self):
        self.logger = TradingBotLogger("ComprehensiveTesting")
        self.test_results = {
            "system_initialization": {"status": "NOT_TESTED", "components": []},
            "core_components": {"status": "NOT_TESTED", "components": []},
            "ai_systems": {"status": "NOT_TESTED", "components": []},
            "trading_systems": {"status": "NOT_TESTED", "components": []},
            "data_systems": {"status": "NOT_TESTED", "components": []},
            "api_systems": {"status": "NOT_TESTED", "components": []},
            "integration_tests": {"status": "NOT_TESTED", "components": []},
            "end_to_end_test": {"status": "NOT_TESTED", "components": []},
            "overall_system_score": 0,
            "critical_failures": [],
            "test_summary": {}
        }
        
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run complete comprehensive testing suite"""
        self.logger.info("Starting comprehensive testing suite...")
        
        start_time = time.time()
        
        try:
            # Test 1: System Initialization (mirrors main.py startup)
            await self._test_system_initialization()
            
            # Test 2: Core Components
            await self._test_core_components()
            
            # Test 3: AI Systems
            await self._test_ai_systems()
            
            # Test 4: Trading Systems
            await self._test_trading_systems()
            
            # Test 5: Data Systems
            await self._test_data_systems()
            
            # Test 6: API Systems
            await self._test_api_systems()
            
            # Test 7: Integration Tests
            await self._test_integration()
            
            # Test 8: End-to-End Test (complete main.py simulation)
            await self._test_end_to_end()
            
            # Calculate overall system score
            self._calculate_system_score()
            
            # Generate test summary
            self._generate_test_summary()
            
            total_time = time.time() - start_time
            self.test_results["total_test_time"] = total_time
            
        except Exception as e:
            self.logger.error(f"Comprehensive testing failed: {e}")
            self.test_results["error"] = str(e)
        
        return self.test_results
    
    async def _test_system_initialization(self):
        """Test system initialization (mirrors main.py startup)"""
        try:
            self.logger.info("Testing system initialization...")
            
            components = []
            
            # Test main system import
            try:
                import main_unified_system
                components.append({"name": "MainSystemImport", "status": "PASS"})
            except Exception as e:
                components.append({"name": "MainSystemImport", "status": "FAIL", "error": str(e)})
            
            # Test configuration loading
            try:
                from bybit_bot.core.config import BotConfig
                config = BotConfig()
                components.append({"name": "ConfigurationLoading", "status": "PASS"})
            except Exception as e:
                components.append({"name": "ConfigurationLoading", "status": "FAIL", "error": str(e)})
            
            # Test logger initialization
            try:
                from bybit_bot.core.logger import TradingBotLogger
                logger = TradingBotLogger("TestLogger")
                components.append({"name": "LoggerInitialization", "status": "PASS"})
            except Exception as e:
                components.append({"name": "LoggerInitialization", "status": "FAIL", "error": str(e)})
            
            # Test database manager
            try:
                from bybit_bot.database.connection import DatabaseManager
                components.append({"name": "DatabaseManager", "status": "PASS"})
            except Exception as e:
                components.append({"name": "DatabaseManager", "status": "FAIL", "error": str(e)})
            
            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)
            
            self.test_results["system_initialization"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"System initialization test: {passed_count}/{total_count} components passed")
            
        except Exception as e:
            self.logger.error(f"System initialization test failed: {e}")
            self.test_results["system_initialization"]["status"] = "FAIL"
    
    async def _test_core_components(self):
        """Test core system components"""
        try:
            self.logger.info("Testing core components...")
            
            components = []
            
            # Test bot manager
            try:
                from bybit_bot.core.bot_manager import BotManager
                components.append({"name": "BotManager", "status": "PASS"})
            except Exception as e:
                components.append({"name": "BotManager", "status": "FAIL", "error": str(e)})
            
            # Test autonomy engine
            try:
                from bybit_bot.core.autonomy_engine import AutonomyEngine
                components.append({"name": "AutonomyEngine", "status": "PASS"})
            except Exception as e:
                components.append({"name": "AutonomyEngine", "status": "FAIL", "error": str(e)})
            
            # Test self-healing system
            try:
                from bybit_bot.core.self_healing import SelfHealingSystem
                components.append({"name": "SelfHealingSystem", "status": "PASS"})
            except Exception as e:
                components.append({"name": "SelfHealingSystem", "status": "FAIL", "error": str(e)})
            
            # Test code optimizer
            try:
                from bybit_bot.core.code_optimizer import CodeOptimizer
                components.append({"name": "CodeOptimizer", "status": "PASS"})
            except Exception as e:
                components.append({"name": "CodeOptimizer", "status": "FAIL", "error": str(e)})
            
            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)
            
            self.test_results["core_components"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"Core components test: {passed_count}/{total_count} components passed")
            
        except Exception as e:
            self.logger.error(f"Core components test failed: {e}")
            self.test_results["core_components"]["status"] = "FAIL"
    
    async def _test_ai_systems(self):
        """Test AI systems"""
        try:
            self.logger.info("Testing AI systems...")
            
            components = []
            
            # Test SuperGPT integration
            try:
                from bybit_bot.ai.supergpt_integration import SuperGPTIntegration
                components.append({"name": "SuperGPTIntegration", "status": "PASS"})
            except Exception as e:
                components.append({"name": "SuperGPTIntegration", "status": "FAIL", "error": str(e)})
            
            # Test memory manager
            try:
                from bybit_bot.ai.memory_manager import PersistentMemoryManager
                components.append({"name": "PersistentMemoryManager", "status": "PASS"})
            except Exception as e:
                components.append({"name": "PersistentMemoryManager", "status": "FAIL", "error": str(e)})
            
            # Test meta-cognition engine
            try:
                from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
                components.append({"name": "MetaCognitionEngine", "status": "PASS"})
            except Exception as e:
                components.append({"name": "MetaCognitionEngine", "status": "FAIL", "error": str(e)})
            
            # Test OpenRouter client
            try:
                from bybit_bot.ai.openrouter_client import OpenRouterClient
                components.append({"name": "OpenRouterClient", "status": "PASS"})
            except Exception as e:
                components.append({"name": "OpenRouterClient", "status": "FAIL", "error": str(e)})
            
            # Test market predictor
            try:
                from bybit_bot.ml.market_predictor import MarketPredictor
                components.append({"name": "MarketPredictor", "status": "PASS"})
            except Exception as e:
                components.append({"name": "MarketPredictor", "status": "FAIL", "error": str(e)})
            
            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)
            
            self.test_results["ai_systems"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"AI systems test: {passed_count}/{total_count} components passed")
            
        except Exception as e:
            self.logger.error(f"AI systems test failed: {e}")
            self.test_results["ai_systems"]["status"] = "FAIL"
    
    async def _test_trading_systems(self):
        """Test trading systems"""
        try:
            self.logger.info("Testing trading systems...")
            
            components = []
            
            # Test enhanced Bybit client
            try:
                from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
                components.append({"name": "EnhancedBybitClient", "status": "PASS"})
            except Exception as e:
                components.append({"name": "EnhancedBybitClient", "status": "FAIL", "error": str(e)})
            
            # Test strategy manager
            try:
                from bybit_bot.strategies.strategy_manager import StrategyManager
                components.append({"name": "StrategyManager", "status": "PASS"})
            except Exception as e:
                components.append({"name": "StrategyManager", "status": "FAIL", "error": str(e)})
            
            # Test adaptive strategy engine
            try:
                from bybit_bot.strategies.adaptive_strategy_engine import AdaptiveStrategyEngine
                components.append({"name": "AdaptiveStrategyEngine", "status": "PASS"})
            except Exception as e:
                components.append({"name": "AdaptiveStrategyEngine", "status": "FAIL", "error": str(e)})
            
            # Test risk manager
            try:
                from bybit_bot.risk.advanced_risk_manager import AdvancedRiskManager
                components.append({"name": "AdvancedRiskManager", "status": "PASS"})
            except Exception as e:
                components.append({"name": "AdvancedRiskManager", "status": "FAIL", "error": str(e)})
            
            # Test profit engines
            try:
                from bybit_bot.profit_maximization.advanced_profit_engine import AdvancedProfitEngine
                components.append({"name": "AdvancedProfitEngine", "status": "PASS"})
            except Exception as e:
                components.append({"name": "AdvancedProfitEngine", "status": "FAIL", "error": str(e)})
            
            try:
                from bybit_bot.profit_maximization.hyper_profit_engine import HyperProfitEngine
                components.append({"name": "HyperProfitEngine", "status": "PASS"})
            except Exception as e:
                components.append({"name": "HyperProfitEngine", "status": "FAIL", "error": str(e)})
            
            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)
            
            self.test_results["trading_systems"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }
            
            self.logger.info(f"Trading systems test: {passed_count}/{total_count} components passed")
            
        except Exception as e:
            self.logger.error(f"Trading systems test failed: {e}")
            self.test_results["trading_systems"]["status"] = "FAIL"

    async def _test_data_systems(self):
        """Test data systems"""
        try:
            self.logger.info("Testing data systems...")

            components = []

            # Test market data crawler
            try:
                from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
                components.append({"name": "MarketDataCrawler", "status": "PASS"})
            except Exception as e:
                components.append({"name": "MarketDataCrawler", "status": "FAIL", "error": str(e)})

            # Test news sentiment crawler
            try:
                from bybit_bot.data_crawler.news_sentiment_crawler import NewsSentimentCrawler
                components.append({"name": "NewsSentimentCrawler", "status": "PASS"})
            except Exception as e:
                components.append({"name": "NewsSentimentCrawler", "status": "FAIL", "error": str(e)})

            # Test social sentiment crawler
            try:
                from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
                components.append({"name": "SocialSentimentCrawler", "status": "PASS"})
            except Exception as e:
                components.append({"name": "SocialSentimentCrawler", "status": "FAIL", "error": str(e)})

            # Test economic data crawler
            try:
                from bybit_bot.data_crawler.economic_data_crawler import EconomicDataCrawler
                components.append({"name": "EconomicDataCrawler", "status": "PASS"})
            except Exception as e:
                components.append({"name": "EconomicDataCrawler", "status": "FAIL", "error": str(e)})

            # Test performance analyzer
            try:
                from bybit_bot.analytics.performance_analyzer import PerformanceAnalyzer
                components.append({"name": "PerformanceAnalyzer", "status": "PASS"})
            except Exception as e:
                components.append({"name": "PerformanceAnalyzer", "status": "FAIL", "error": str(e)})

            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)

            self.test_results["data_systems"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Data systems test: {passed_count}/{total_count} components passed")

        except Exception as e:
            self.logger.error(f"Data systems test failed: {e}")
            self.test_results["data_systems"]["status"] = "FAIL"

    async def _test_api_systems(self):
        """Test API systems"""
        try:
            self.logger.info("Testing API systems...")

            components = []

            # Test Bybit API connectivity
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    async with session.get("https://api.bybit.com/v5/market/time") as response:
                        if response.status == 200:
                            components.append({"name": "BybitAPIConnectivity", "status": "PASS"})
                        else:
                            components.append({"name": "BybitAPIConnectivity", "status": "FAIL", "error": f"HTTP {response.status}"})
            except Exception as e:
                components.append({"name": "BybitAPIConnectivity", "status": "FAIL", "error": str(e)})

            # Test WebSocket capabilities
            try:
                import websockets
                components.append({"name": "WebSocketSupport", "status": "PASS"})
            except Exception as e:
                components.append({"name": "WebSocketSupport", "status": "FAIL", "error": str(e)})

            # Test MCP system
            try:
                from bybit_bot.mcp.safe_mcp_initializer import SafeMCPInitializer
                components.append({"name": "MCPSystem", "status": "PASS"})
            except Exception as e:
                components.append({"name": "MCPSystem", "status": "FAIL", "error": str(e)})

            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)

            self.test_results["api_systems"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"API systems test: {passed_count}/{total_count} components passed")

        except Exception as e:
            self.logger.error(f"API systems test failed: {e}")
            self.test_results["api_systems"]["status"] = "FAIL"

    async def _test_integration(self):
        """Test system integration"""
        try:
            self.logger.info("Testing system integration...")

            components = []

            # Test agent orchestrator
            try:
                from bybit_bot.agents.agent_orchestrator import AgentOrchestrator
                components.append({"name": "AgentOrchestrator", "status": "PASS"})
            except Exception as e:
                components.append({"name": "AgentOrchestrator", "status": "FAIL", "error": str(e)})

            # Test monitoring systems
            try:
                from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
                components.append({"name": "HardwareMonitor", "status": "PASS"})
            except Exception as e:
                components.append({"name": "HardwareMonitor", "status": "FAIL", "error": str(e)})

            # Test configuration integration
            try:
                from bybit_bot.core.config import BotConfig
                config = BotConfig()
                if hasattr(config, 'trading_enabled') and hasattr(config, 'supergpt_enabled'):
                    components.append({"name": "ConfigurationIntegration", "status": "PASS"})
                else:
                    components.append({"name": "ConfigurationIntegration", "status": "FAIL", "error": "Missing config attributes"})
            except Exception as e:
                components.append({"name": "ConfigurationIntegration", "status": "FAIL", "error": str(e)})

            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)

            self.test_results["integration_tests"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"Integration test: {passed_count}/{total_count} components passed")

        except Exception as e:
            self.logger.error(f"Integration test failed: {e}")
            self.test_results["integration_tests"]["status"] = "FAIL"

    async def _test_end_to_end(self):
        """Test end-to-end system (complete main.py simulation)"""
        try:
            self.logger.info("Testing end-to-end system...")

            components = []

            # Test complete system import and initialization
            try:
                import main_unified_system

                # Test if main system can be instantiated
                # Note: We don't actually run it to avoid side effects
                components.append({"name": "CompleteSystemImport", "status": "PASS"})

            except Exception as e:
                components.append({"name": "CompleteSystemImport", "status": "FAIL", "error": str(e)})

            # Test system readiness
            try:
                # Check if all critical modules are importable
                critical_modules = [
                    "bybit_bot.core.config",
                    "bybit_bot.core.logger",
                    "bybit_bot.exchange.enhanced_bybit_client",
                    "bybit_bot.ai.supergpt_integration",
                    "bybit_bot.profit_maximization.advanced_profit_engine"
                ]

                all_imported = True
                for module in critical_modules:
                    try:
                        __import__(module)
                    except:
                        all_imported = False
                        break

                if all_imported:
                    components.append({"name": "SystemReadiness", "status": "PASS"})
                else:
                    components.append({"name": "SystemReadiness", "status": "FAIL", "error": "Critical modules failed"})

            except Exception as e:
                components.append({"name": "SystemReadiness", "status": "FAIL", "error": str(e)})

            # Test system configuration completeness
            try:
                from bybit_bot.core.config import BotConfig
                config = BotConfig()

                required_attrs = ['trading_enabled', 'supergpt_enabled', 'api_host', 'api_port']
                has_all_attrs = all(hasattr(config, attr) for attr in required_attrs)

                if has_all_attrs:
                    components.append({"name": "ConfigurationCompleteness", "status": "PASS"})
                else:
                    components.append({"name": "ConfigurationCompleteness", "status": "FAIL", "error": "Missing config attributes"})

            except Exception as e:
                components.append({"name": "ConfigurationCompleteness", "status": "FAIL", "error": str(e)})

            passed_count = len([c for c in components if c["status"] == "PASS"])
            total_count = len(components)

            self.test_results["end_to_end_test"] = {
                "status": "PASS" if passed_count == total_count else "PARTIAL" if passed_count > 0 else "FAIL",
                "components": components,
                "score": passed_count / total_count if total_count > 0 else 0
            }

            self.logger.info(f"End-to-end test: {passed_count}/{total_count} components passed")

        except Exception as e:
            self.logger.error(f"End-to-end test failed: {e}")
            self.test_results["end_to_end_test"]["status"] = "FAIL"

    def _calculate_system_score(self):
        """Calculate overall system score"""
        try:
            categories = [
                "system_initialization", "core_components", "ai_systems",
                "trading_systems", "data_systems", "api_systems",
                "integration_tests", "end_to_end_test"
            ]

            total_score = 0
            valid_categories = 0

            for category in categories:
                if category in self.test_results and "score" in self.test_results[category]:
                    total_score += self.test_results[category]["score"]
                    valid_categories += 1

            self.test_results["overall_system_score"] = total_score / valid_categories if valid_categories > 0 else 0

            self.logger.info(f"Overall system score: {self.test_results['overall_system_score']:.2%}")

        except Exception as e:
            self.logger.error(f"Error calculating system score: {e}")
            self.test_results["overall_system_score"] = 0

    def _generate_test_summary(self):
        """Generate comprehensive test summary"""
        try:
            summary = {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "partial_tests": 0,
                "critical_failures": [],
                "system_status": "UNKNOWN"
            }

            # Count test results
            for category, results in self.test_results.items():
                if isinstance(results, dict) and "status" in results:
                    summary["total_tests"] += 1

                    if results["status"] == "PASS":
                        summary["passed_tests"] += 1
                    elif results["status"] == "FAIL":
                        summary["failed_tests"] += 1
                        summary["critical_failures"].append(category)
                    elif results["status"] == "PARTIAL":
                        summary["partial_tests"] += 1

            # Determine overall system status
            if summary["failed_tests"] == 0:
                if summary["partial_tests"] == 0:
                    summary["system_status"] = "FULLY_OPERATIONAL"
                else:
                    summary["system_status"] = "OPERATIONAL_WITH_WARNINGS"
            else:
                if summary["failed_tests"] > summary["passed_tests"]:
                    summary["system_status"] = "CRITICAL_FAILURES"
                else:
                    summary["system_status"] = "OPERATIONAL_WITH_ISSUES"

            self.test_results["test_summary"] = summary

        except Exception as e:
            self.logger.error(f"Error generating test summary: {e}")
            self.test_results["test_summary"] = {"error": "Failed to generate summary"}

async def main():
    """Main testing function"""
    testing = ComprehensiveTestingSuite()
    results = await testing.run_comprehensive_tests()

    print("\n" + "="*80)
    print("COMPREHENSIVE TESTING SUITE RESULTS")
    print("="*80)

    # Display results by category
    categories = [
        "system_initialization", "core_components", "ai_systems",
        "trading_systems", "data_systems", "api_systems",
        "integration_tests", "end_to_end_test"
    ]

    for category in categories:
        if category in results:
            result = results[category]
            status = result.get("status", "UNKNOWN")
            score = result.get("score", 0)
            print(f"{category.replace('_', ' ').title():<30}: {status:<8} ({score:.1%})")

    print("-"*80)
    print(f"Overall System Score: {results['overall_system_score']:.1%}")
    print(f"Total Test Time: {results.get('total_test_time', 0):.2f}s")
    print("-"*80)

    # Display test summary
    if "test_summary" in results:
        summary = results["test_summary"]
        print(f"\nTEST SUMMARY:")
        print(f"Total Tests: {summary.get('total_tests', 0)}")
        print(f"Passed: {summary.get('passed_tests', 0)}")
        print(f"Failed: {summary.get('failed_tests', 0)}")
        print(f"Partial: {summary.get('partial_tests', 0)}")
        print(f"System Status: {summary.get('system_status', 'UNKNOWN')}")

        if summary.get("critical_failures"):
            print(f"\nCRITICAL FAILURES:")
            for i, failure in enumerate(summary["critical_failures"], 1):
                print(f"{i}. {failure.replace('_', ' ').title()}")

    print("="*80)

    # Save results to file
    with open("comprehensive_testing_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)

    return results

if __name__ == "__main__":
    asyncio.run(main())
